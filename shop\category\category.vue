<template>
  <view class="category-container">
    <!-- 自定义导航栏 -->
    <view
      class="navbar"
      v-if="showCustomNavbar"
      :style="{
        top: isPC ? '0' : showSystemNav ? customNavHeight.value + 'px' : '0',
        position: isPC ? 'static' : 'fixed',
      }"
    >
      <text class="logo ripple" @click="backHome">绿境商城</text>
      <view class="search-bar">
        <input
          class="search-input"
          placeholder="搜索商品/设备/种子/营养液..."
          v-model="searchKeyword"
          @confirm="handleSearch"
        />
        <uni-icons
          type="search"
          size="18"
          color="#888"
          @click="handleSearch"
          class="ripple"
        />
        <uni-icons
          v-if="searchKeyword"
          type="clear"
          size="16"
          color="#888"
          @click="clearSearch"
          class="clear-icon ripple"
        />
      </view>
      <view class="nav-actions">
        <view class="cart-icon-container" @click="goToCart">
          <uni-icons type="cart" size="26" color="#222" />
          <view class="cart-count" v-if="cartCount > 0">{{ cartCount }}</view>
        </view>
      </view>
    </view>

    <!-- 分类导航卡片 -->
    <view class="category-card">
      <view class="card-title">商品分类</view>
      <view
        class="category-grid"
        :class="{ 'wrap-grid': platform === 'mp-weixin' }"
      >
        <view
          class="category-item ripple"
          v-for="item in categories"
          :key="item.id"
          :class="{ active: String(currentCategory) === String(item.id) }"
          @click="changeCategory(item.id)"
        >
          <text>{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 二级分类和排序条 -->
    <view class="second-category-bar">
      <view
        class="second-category-item"
        v-for="item in secondCategories"
        :key="item.id"
        :class="{ active: currentSecondCategory === item.id }"
        @click="changeSecondCategory(item.id)"
      >
        {{ item.name }}
      </view>
      <view class="price-sort" @click="togglePriceOrder">
        价格
        <uni-icons
          :type="priceOrder === 'asc' ? 'arrow-up' : 'arrow-down'"
          size="18"
        />
      </view>
    </view>

    <!-- 商品列表 -->
    <scroll-view class="goods-list" scroll-y @scrolltolower="loadMore">
      <view class="goods-grid">
        <view
          class="goods-card ripple"
          v-for="item in goodsList"
          :key="item.id"
          @click="goToDetail(item.id)"
        >
          <view class="card-ribbon" v-if="item.isNew">新品</view>
          <view class="card-ribbon hot-ribbon" v-if="item.isHot">热销</view>
          <view class="goods-img">
            <image :src="item.img || item.cover" mode="aspectFill" />
          </view>
          <view class="goods-info">
            <text class="goods-name">{{ item.name }}</text>
            <text class="goods-desc">{{ item.desc }}</text>
            <view class="goods-price">
              <text class="price-text">¥{{ item.price }}</text>
              <text v-if="item.vipPrice" class="vip-price"
                >会员价: ¥{{ item.vipPrice }}</text
              >
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loading">
        <text>加载中...</text>
      </view>

      <!-- 空状态提示 -->
      <view class="empty-state" v-if="goodsList.length === 0 && !loading">
        <uni-icons type="search" size="50" color="#ccc" />
        <text class="empty-text">暂无相关商品</text>
      </view>

      <view v-if="platform === 'windows'" class="right-nav">
        <view
          class="nav-item"
          v-for="nav in footerNav"
          :key="nav.name"
          @click="navigateTo(nav.name)"
        >
          <uni-icons
            :type="nav.icon"
            size="28"
            :color="nav.active ? '#1abc9c' : '#888'"
          />
          <text :class="{ active: nav.active }">{{ nav.name }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onBeforeMount } from "vue";
import { apiGetShopCategory, apiGetShop, apiGetShopPage } from "@/api/store.js";
import { onLoad } from "@dcloudio/uni-app";
// 搜索关键词
const searchKeyword = ref("");
// 加载状态
const loading = ref(false);
// 当前分类ID
const currentCategory = ref("0");
const currentSecondCategory = ref("all");
// 购物车数量
const cartCount = ref(3);
let initialCategoryId = null;
// 分类数据
const categories = ref([
  {
    id: "1",
    name: "全部商品",
  },
  {
    id: "2",
    name: "设备",
  },
  {
    id: "3",
    name: "种子",
  },
  {
    id: "4",
    name: "营养液",
  },
  {
    id: "5",
    name: "配件",
  },
  {
    id: "6",
    name: "耗材",
  },
  {
    id: "7",
    name: "农产品",
  },
  {
    id: "8",
    name: "农具",
  },
  {
    id: "9",
    name: "设施",
  },
]);

// 二级分类
const secondCategories = ref([
  {
    id: "all",
    name: "全部",
  },
  {
    id: "new",
    name: "新品",
  },
  {
    id: "hot",
    name: "热销",
  },
]);

// 价格排序
const priceOrder = ref("asc");

const allGoods = ref([]); // 所有商品
const goodsList = ref([]); // 当前展示的商品（分页）

// 获取导航栏高度（微信小程序）
const customNavHeight = ref(0);
const searchBarHeight = ref(60);

// 是否显示系统导航栏
const showSystemNav = ref(false);
// 是否显示自定义导航栏
const showCustomNavbar = ref(false);

// 是否为PC端
const isPC = computed(() => {
  if (typeof window !== "undefined") {
    return window.innerWidth >= 1024;
  }
  return false;
});

// 重置分页
const resetAndLoad = () => {
  pageNum.value = 1;
  hasMore.value = true;
  filterGoods();
};

// 获取平台类型
const getPlatform = () => {
  if (typeof uni !== "undefined") {
    const platform = uni.getSystemInfoSync().platform;
    return platform;
  }
  return "h5"; // 默认返回h5
};
// 获取导航栏高度
const getNavHeight = () => {
  if (typeof uni !== "undefined") {
    try {
      const systemInfo = uni.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight;
      const isIphoneX = /iphone x/i.test(systemInfo.model.toLowerCase());
      const navHeight = isIphoneX ? 88 : 44;
      customNavHeight.value = statusBarHeight + navHeight;
    } catch (e) {
      console.error("获取系统信息失败:", e);
    }
  }
};

// 获取分类
const getShopCategory = async () => {
  try {
    const response = await apiGetShopCategory();
    if (response.code === 200) {
      categories.value = [
        {
          id: "0",
          name: "全部商品",
        },
        ...response.rows,
      ];
      if (initialCategoryId) {
        currentCategory.value = initialCategoryId;
      }
    } else {
      uni.showToast({
        title: "获取商品分类失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取商品分类数据失败:", error);
    uni.showToast({
      title: "获取商品分类失败",
      icon: "none",
    });
  }
};

// 获取商品
const getGoods = async () => {
  loading.value = true;
  try {
    const response = await apiGetShop(); // 一次性获取所有商品
    if (response.code === 200) {
      allGoods.value = response.rows;
      resetAndLoad();
    }
  } finally {
    loading.value = false;
  }
};

// 筛选 + 分页
const filterGoods = () => {
  let filtered = allGoods.value;

  // 主分类
  if (currentCategory.value !== "0") {
    filtered = filtered.filter(
      (item) => String(item.categoryId) === String(currentCategory.value)
    );
  }
  // 二级分类
  if (currentSecondCategory.value === "new") {
    filtered = filtered.filter((item) => item.isNew == 1);
  } else if (currentSecondCategory.value === "hot") {
    filtered = filtered.filter((item) => item.isHot == 1);
  }
  // 搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase();
    filtered = filtered.filter(
      (item) =>
        item.name.toLowerCase().includes(keyword) ||
        (item.desc && item.desc.toLowerCase().includes(keyword))
    );
  }
  // 排序
  filtered = filtered.sort((a, b) =>
    priceOrder.value === "asc" ? a.price - b.price : b.price - a.price
  );

  // 分页
  const start = (pageNum.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  goodsList.value = filtered.slice(0, end);
  hasMore.value = end < filtered.length;
};

// 切换主分类
const changeCategory = (id) => {
  currentCategory.value = id;
  resetAndLoad();
};

// 切换二级分类
const changeSecondCategory = (id) => {
  currentSecondCategory.value = id;
  resetAndLoad();
};

// 切换价格排序
const togglePriceOrder = () => {
  priceOrder.value = priceOrder.value === "asc" ? "desc" : "asc";
  resetAndLoad();
};

// 导航栏数据
const footerNav = ref([
  {
    name: "首页",
    icon: "home-filled",
    active: false,
  },
  {
    name: "分类",
    icon: "bars",
    active: true,
  },
  {
    name: "购物车",
    icon: "cart",
    active: false,
  },
  {
    name: "我的",
    icon: "person",
    active: false,
  },
]);

// 导航跳转
const navigateTo = (name) => {
  footerNav.value.forEach((nav) => {
    nav.active = nav.name === name;
  });
  if (name === "首页") {
    uni.navigateTo({
      url: "/shop/homepage/homepage",
    });
  } else if (name === "购物车") {
    uni.navigateTo({
      url: "/shop/cart/cart",
    });
  } else if (name === "我的") {
    uni.navigateTo({
      url: "/shop/my/my",
    });
  }
};

const pageNum = ref(1);
const pageSize = ref(5);
const hasMore = ref(true); // 是否还有更多数据

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) return;
  resetAndLoad();
  // 这里应该是搜索API调用
  loading.value = true;
  setTimeout(() => {
    // 模拟搜索结果
    const filteredGoods = goodsList.value.filter(
      (item) =>
        item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        item.desc.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
    goodsList.value = filteredGoods;
    loading.value = false;
  }, 500);
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = "";
  resetAndLoad();
  // 恢复原始数据
  initData();
};

// 加载更多
const loadMore = () => {
  if (loading.value || !hasMore.value) return;
  loading.value = true;
  setTimeout(() => {
    pageNum.value += 1;
    filterGoods();
    loading.value = false;
  }, 300);
};

// 跳转到商品详情
const goToDetail = (goodsId) => {
  if (typeof uni !== "undefined") {
    uni.navigateTo({
      url: `/shop/goods/goods?id=${goodsId}`,
    });
  }
};

// 跳转到购物车
const goToCart = () => {
  if (typeof uni !== "undefined") {
    uni.navigateTo({
      url: "/shop/cart/cart",
    });
  }
};

// 回到主页
const backHome = () => {
  console.log("platform的值为:", platform.value);
  if (platform.value === "devtools") {
    uni.switchTab({
      url: "/pages/store/store",
    });
  } else if (platform.value === "windows") {
    uni.navigateTo({
      url: "/shop/homepage/homepage",
    });
  } else if (platform.value === "android") {
    uni.switchTab({
      url: "/pages/store/store",
    });
  } else if (platform.value === "ios") {
    uni.switchTab({
      url: "/pages/store/store",
    });
  }
};
const platform = ref("unknown");

onBeforeMount(() => {
  platform.value = getPlatform();
  console.log("platform.value有值嘛", platform.value);
});

onLoad((options) => {
  if (options.categoryId) {
    initialCategoryId = String(options.categoryId);
    currentCategory.value = initialCategoryId;
  }
  // 判断平台类型

  // 在PC端只显示自定义导航栏
  if (isPC.value) {
    showCustomNavbar.value = true;
    showSystemNav.value = false;
  }
  // 在微信小程序显示系统导航栏和自定义导航栏
  else if (platform.value === "mp-weixin") {
    showCustomNavbar.value = true;
    showSystemNav.value = true;
  }
  // 在其他平台只显示自定义导航栏
  else {
    showCustomNavbar.value = true;
    showSystemNav.value = false;
  }

  getNavHeight();
  getShopCategory();
  getGoods();
});
watch(currentCategory, filterGoods);
</script>

<style lang="scss" scoped>
/* 全局变量 */
:root {
  --primary-color: #1abc9c;
  --secondary-color: #e67e22;
  --text-color: #2c3e50;
  --subtext-color: #7f8c8d;
  --background-color: #f7fafc;
  --card-bg: #ffffff;
  --border-radius: 16px;
  --card-shadow: 0 6px 16px rgba(30, 200, 180, 0.12);
  --btn-primary: linear-gradient(90deg, #1abc9c 0%, #16d9e3 100%);
  --btn-secondary: linear-gradient(90deg, #e67e22 0%, #d35400 100%);
}

.category-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-top: 60px;
  box-sizing: border-box;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.logo {
  font-size: 18px;
  font-weight: 600;
  color: #1abc9c;
  padding: 8px 0;
  cursor: pointer;
}

.search-bar {
  flex: 1;
  margin: 0 12px;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 0 12px;
  height: 36px;
  max-width: 500px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  background: transparent;
  margin-left: 4px;
  height: 100%;
  padding: 0 4px;
}

.clear-icon {
  margin-left: 6px;
  padding: 4px;
  cursor: pointer;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
  }
}

.cart-icon {
  padding: 8px;
  cursor: pointer;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
  }
}

.category-card {
  margin: 12px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-title {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 8px;
  padding: 8px;
}

.wrap-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 8px;
  white-space: normal !important;
  align-items: stretch;
}

.category-item {
  padding: 6px 12px;
  margin: 4px;
  border-radius: 16px;
  font-size: 13px;
  color: #666;
  background-color: #f5f5f5;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.category-item.active {
  color: #fff;
  background-color: #1abc9c;
}

.goods-list {
  padding: 0 12px;
  box-sizing: border-box;
  width: 100%;
  height: calc(100vh - 60px);
}

.goods-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.goods-card {
  width: calc(50% - 6px);
  margin-bottom: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.card-ribbon {
  position: absolute;
  top: 10px;
  left: 10px;
  background: var(--primary-color);
  color: #fff;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  z-index: 1;
}

.hot-ribbon {
  background: var(--secondary-color);
}

.goods-img {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;

  image {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.goods-info {
  padding: 8px;
}

.goods-name {
  font-size: 15px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 4px;
  /* 固定两行高度并省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 20px;
  /* 2行高度，具体可根据字体大小微调 */
  line-height: 20px;
  /* 具体可根据实际字体大小微调 */
}

.goods-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  min-height: 16px;
  /* 2行高度，按实际字体大小微调 */
  line-height: 16px;
}

.goods-price {
  display: flex;
  align-items: baseline;
}

.price-text {
  font-size: 15px;
  font-weight: 600;
  color: #e67e22;
}

.vip-price {
  font-size: 12px;
  color: #1abc9c;
  margin-left: 6px;
}

.loading-more {
  padding: 16px 0;
  text-align: center;
  color: #999;
  font-size: 13px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #999;
}

.empty-text {
  margin-top: 12px;
  font-size: 14px;
}

// 波纹效果
.ripple {
  position: relative;
  overflow: hidden;

  &::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #000 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 1s;
  }

  &:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
  }
}

// 移动端适配
@media screen and (max-width: 375px) {
  .navbar {
    padding: 8px 12px;

    .logo {
      font-size: 16px;
    }

    .search-bar {
      height: 32px;
      margin: 0 8px;
    }

    .cart-icon {
      padding: 6px;
    }
  }

  .category-item {
    padding: 4px 10px;
    margin: 3px;
    font-size: 12px;
    border-radius: 14px;
  }

  .goods-card {
    width: calc(50% - 4px);
    margin-bottom: 8px;
    border-radius: 10px;
  }

  .search-bar {
    .search-input {
      font-size: 13px;
      padding: 0 3px;
    }

    .clear-icon {
      margin-left: 4px;
      padding: 3px;
    }
  }
}

@media screen and (min-width: 376px) and (max-width: 414px) {
  .navbar {
    padding: 10px 14px;

    .logo {
      font-size: 17px;
    }

    .search-bar {
      height: 34px;
      margin: 0 10px;
    }
  }

  .category-item {
    padding: 5px 11px;
    margin: 3.5px;
    font-size: 12.5px;
  }

  .goods-card {
    width: calc(50% - 5px);
    margin-bottom: 10px;
    border-radius: 11px;
  }

  .search-bar {
    .search-input {
      font-size: 13.5px;
      padding: 0 3.5px;
    }
  }
}

@media screen and (min-width: 415px) and (max-width: 768px) {
  .navbar {
    padding: 12px 16px;

    .logo {
      font-size: 18px;
    }

    .search-bar {
      height: 36px;
      margin: 0 12px;
    }
  }

  .category-item {
    padding: 6px 12px;
    margin: 4px;
    font-size: 13px;
  }

  .goods-card {
    width: calc(50% - 6px);
    margin-bottom: 12px;
    border-radius: 12px;
  }
}

// PC端适配
@media screen and (min-width: 1024px) {
  .category-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 60px;
  }

  .navbar {
    position: static;
    padding: 48px 0 0 0;
    background: transparent;
    box-shadow: none;
  }

  .logo {
    font-size: 42px;
  }

  .search-bar {
    height: 56px;
    margin: 0 32px;
    padding: 0 20px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(30, 200, 180, 0.06);
  }

  .search-input {
    font-size: 18px;
  }

  .cart-icon {
    size: 32px;
  }

  .category-card {
    margin: 40px 0;
  }

  .card-title {
    padding: 16px;
    font-size: 18px;
  }

  .category-grid {
    padding: 12px 8px;
  }

  .category-item {
    padding: 8px 16px;
    margin: 8px;
    font-size: 14px;
  }

  .goods-list {
    padding: 0;
  }

  .goods-card {
    width: 32%;
    margin-bottom: 24px;
  }

  .goods-img {
    height: 180px;
    padding-bottom: 0;

    image {
      position: static;
      height: 180px;
    }
  }

  .goods-info {
    padding: 14px;
  }

  .goods-name {
    font-size: 18px;
  }

  .goods-desc {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .price-text {
    font-size: 18px;
  }

  .vip-price {
    font-size: 14px;
  }

  .loading-more {
    padding: 24px 0;
    font-size: 15px;
  }

  .empty-state {
    height: 320px;
  }

  .empty-text {
    margin-top: 18px;
    font-size: 17px;
  }
}

// 安全区域适配
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .category-container {
    padding-bottom: constant(safe-area-inset-bottom);
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .category-container {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.right-nav {
  position: fixed;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  width: 100px;
  height: auto;
  min-height: 280px;
  flex-direction: column;
  border-radius: 28px;
  box-shadow: -4px 0 24px rgba(30, 200, 180, 0.16);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(16px);
  padding: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.nav-item {
  width: 100%;
  margin: 14px 0;
  padding: 14px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: rgba(26, 188, 156, 0.05);
  }

  .active {
    color: #1abc9c;
    font-weight: 700;
  }
}

.nav-actions {
  display: flex;
  align-items: center;
}

.user-icon {
  margin-right: 16px;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.1);
  }
}

.cart-icon-container {
  position: relative;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.1);
  }
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #e67e22;
  color: #fff;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.second-category-bar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;

  .second-category-item {
    margin-right: 16px;
    padding: 4px 12px;
    border-radius: 12px;
    background: #f5f5f5;
    color: #666;
    cursor: pointer;

    &.active {
      background: #1abc9c;
      color: #fff;
    }
  }

  .price-sort {
    margin-left: auto;
    display: flex;
    align-items: center;
    color: #e67e22;
    cursor: pointer;
    font-weight: 600;
  }
}
</style>
