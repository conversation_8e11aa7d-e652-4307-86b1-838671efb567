"use strict";const d=require("../common/vendor.js");function a(e,n){return e.endsWith("/")&&n.startsWith("/")?e+n.substring(1):!e.endsWith("/")&&!n.startsWith("/")?e+"/"+n:e+n}function f(e={}){const n="http://192.168.1.42:8080";let{url:o,method:u="GET",data:i={},header:c={}}=e;if(["GET","DELETE"].includes(u)){const s=Object.keys(i).map(r=>`${encodeURIComponent(r)}=${encodeURIComponent(i[r])}`).join("&");s&&(o+=(o.includes("?")?"&":"?")+s),console.log("Final request URL:",o),i={}}o=a(n,o);let l=d.index.getStorageSync("userData");return c.Authorization=l.token,c["Content-Type"]="application/json",new Promise((s,r)=>{d.index.request({url:o,method:u,data:["POST","PUT"].includes(u)?JSON.stringify(i):i,header:c,success:t=>{t.data.code===200?s(t.data):(console.log("\u9519\u8BEF\u63D0\u793A",t.data.msg),r(t.data))},fail:t=>{console.error("request fail:",t),r(t)}})})}exports.request=f;
