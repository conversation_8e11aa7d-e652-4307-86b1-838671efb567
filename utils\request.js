function joinUrl(base, path) {
	if (base.endsWith('/') && path.startsWith('/')) {
		return base + path.substring(1);
	} else if (!base.endsWith('/') && !path.startsWith('/')) {
		return base + '/' + path;
	} else {
		return base + path;
	}
}

export function request(config = {}) {
	// const Base_url = "http://192.168.1.224:9920";
	const Base_url = "http://192.168.1.42:8080";
	// const Base_url = "https://ljht.mydmc.cn:8443/prod-api";
	let {
		url,
		method = 'GET', // 默认方法为 GET
		data = {},
		header = {}
	} = config;

	if (['GET', 'DELETE'].includes(method)) {
		const queryString = Object.keys(data)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
			.join('&');
		if (queryString) {
			url += (url.includes('?') ? '&' : '?') + queryString;
		}
		console.log('Final request URL:', url);
		data = {}; // GET 和 DELETE 请求不需要请求体
	}

	url = joinUrl(Base_url, url);
	// 添加 token 到 header
	let token = uni.getStorageSync('userData');
	header['Authorization'] = token.token;
	header['Content-Type'] = 'application/json'; // 设置 Content-Type 为 JSON

	return new Promise((resolve, reject) => {
		uni.request({
			url,
			method,
			data: ['POST', 'PUT'].includes(method) ? JSON.stringify(data) : data,
			header,
			success: res => {
				if (res.data.code === 200) {
					resolve(res.data); // 请求成功，返回数据
				} else {
					console.log("错误提示",res.data.msg);
					reject(res.data); // 请求失败，抛出错误
				}
			},
			fail: res => {
				console.error('request fail:', res);
				reject(res); // 网络错误等，抛出错误
			}
		});
	});
}